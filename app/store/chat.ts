import { create } from 'zustand';
import { ChatType } from '@/types/llm';
import { updateChatInServer } from '@/app/[workspace]/chat/actions/chat';

interface IChatStore {
  chat: ChatType | null;
  webSearchEnabled: boolean;
  builtInImageGen: boolean;
  builtInWebSearch: boolean;
  thinkingConfig: 'disabled' | 'light' | 'medium' | 'deep';
  historyType: 'all' | 'none' | 'count';
  historyCount: number;
  setHistoryType: (workspaceId: string, chatId: string, newType: 'all' | 'none' | 'count') => void;
  setHistoryCount: (workspaceId: string, chatId: string, newCount: number) => void;
  setChat: (chat: ChatType) => void;
  setWebSearchEnabled: (flag: boolean) => void;
  setThinkingConfig: (value: 'disabled' | 'light' | 'medium' | 'deep') => void;
  setBuiltInImageGen: (flag: boolean) => void;
  initializeChat: (chatInfo: ChatType) => void;
}

const useChatStore = create<IChatStore>((set) => ({
  chat: null,
  webSearchEnabled: false,
  builtInImageGen: false,
  builtInWebSearch: false,
  thinkingConfig: 'disabled',
  historyType: 'count',
  historyCount: 5,
  setHistoryType: (workspaceId: string, chatId: string, newType: 'all' | 'none' | 'count') => {
    set(() => {
      updateChatInServer(workspaceId, chatId, { historyType: newType })
      return { historyType: newType }
    });
  },
  setHistoryCount: (workspaceId: string, chatId: string, newCount: number) => {
    set(() => {
      updateChatInServer(workspaceId, chatId, { historyCount: newCount })
      return { historyCount: newCount }
    });
  },

  setChat: (chat: ChatType) => {
    set({ chat: chat });
  },

  setWebSearchEnabled: (flag: boolean) => {
    set({ webSearchEnabled: flag });
  },

  setBuiltInImageGen: (flag: boolean) => {
    set({ builtInImageGen: flag });
  },

  setThinkingConfig: (value) => {
    set({ thinkingConfig: value });
  },

  initializeChat: async (chatInfo: ChatType) => {
    set({
      chat: chatInfo,
      historyType: chatInfo.historyType || 'count',
      historyCount: chatInfo.historyCount || 5
    });
  },

}))

export default useChatStore
